import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
import base64
import requests
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
from openai import OpenAI
from collections import Counter


class AzureExamAnalyzer:
    def __init__(self):
        self.hotkey_detected = False
        self.running = True
        self.listener = None
        self.root = None
        self.api_key = "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"
        
        # Define all four models for the pipeline
        self.model_analyzer_1 = "google/gemini-2.5-flash"
        self.model_analyzer_2 = "openai/gpt-5-mini"      # Fictional model name
        self.model_analyzer_3 = "mistralai/mistral-medium-3.1" # Fictional model name
        self.model_decider = "anthropic/claude-sonnet-4"         # User-specified model

        # Initialize OpenAI client for OpenRouter
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )

        # ShareX output directory
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        self.temp_dir = Path(tempfile.gettempdir()) / "azure_exam_analyzer"
        self.temp_dir.mkdir(exist_ok=True)

        # File monitoring
        self.file_observer = None
        self.latest_file = None
        self.processing_lock = threading.Lock()

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()
        

    def setup_root(self):
        """Setup the persistent root window."""
        self.root = tk.Tk()
        self.root.withdraw()
        self.root.title("Azure Exam Analyzer")
        
    def setup_file_monitoring(self):
        """Setup file system monitoring for ShareX output."""
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created ShareX directory: {self.sharex_dir}")
        else:
            print(f"✓ Found ShareX directory: {self.sharex_dir}")

        self.file_handler = ShareXFileHandler(self)
        self.file_observer = Observer()
        self.file_observer.schedule(self.file_handler, str(self.sharex_dir), recursive=False)
        self.file_observer.start()

    def show_answer_bubble(self, answer):
        """Show an auto-expanding bubble that wraps text and respects screen boundaries, with no scrollbar."""
        def create_bubble():
            bubble = tk.Toplevel(self.root)
            bubble.title("Azure Exam Answer")
            bubble.overrideredirect(True)
            bubble.attributes('-topmost', True)
            bubble.attributes('-alpha', 0.95)

            screen_width = bubble.winfo_screenwidth()
            screen_height = bubble.winfo_screenheight()
            max_width = int(screen_width * 0.5)
            max_height = int(screen_height * 0.5)

            outer_frame = tk.Frame(bubble, bg='#2E86AB', relief='raised', bd=3)
            outer_frame.pack(padx=10, pady=10)
            inner_frame = tk.Frame(outer_frame, bg='#A23B72', relief='flat', bd=2)
            inner_frame.pack(padx=5, pady=5)

            answer_label = tk.Label(
                inner_frame, text=f"🎯 {answer.strip()}", font=('Arial', 16), fg='white', bg='#A23B72',
                padx=20, pady=20, justify='left', anchor='nw', wraplength=max_width - 60
            )
            answer_label.pack(expand=True, fill='both')

            def close_bubble():
                try: bubble.destroy()
                except: pass

            close_button = tk.Button(
                inner_frame, text="✕ Close", font=('Arial', 10, 'bold'), fg='white', bg='#8B2635',
                activebackground='#A23B72', activeforeground='white', relief='flat',
                padx=15, pady=5, command=close_bubble, cursor='hand2'
            )
            close_button.pack(pady=(10, 5))

            bubble.update_idletasks()
            final_width = min(bubble.winfo_reqwidth(), max_width)
            final_height = min(bubble.winfo_reqheight(), max_height)
            x = (screen_width - final_width) // 2
            y = 50
            bubble.geometry(f"{final_width}x{final_height}+{x}+{y}")
            
            bubble.bind('<Escape>', lambda e: close_bubble())
            bubble.focus_set()

        if self.root:
            self.root.after(0, create_bubble)


    def on_key_press(self, key):
        """Handle key press events."""
        try:
            if hasattr(key, 'char') and key.char == '\x04': # CTRL+D
                print("Ctrl+D detected!")
                self.hotkey_detected = True
        except Exception:
            pass
    
    def check_hotkey(self):
        """Check for hotkey detection."""
        if self.hotkey_detected:
            self.hotkey_detected = False
            print("Launching ShareX for Azure exam question capture...")
            self.launch_sharex_capture()
        
        if self.running:
            self.root.after(100, self.check_hotkey)
    
    def start_listener(self):
        """Start the keyboard listener."""
        print("Azure Exam Analyzer - ShareX + OpenRouter Integration")
        print("=" * 55)
        print("Press Ctrl+D to capture Azure exam question")
        print("Press Ctrl+C to exit")
        print()
        print(f"📁 Monitoring ShareX directory: {self.sharex_dir}")
        print(f"🤖 Analyzer 1: {self.model_analyzer_1}")
        print(f"🤖 Analyzer 2: {self.model_analyzer_2}")
        print(f"🤖 Analyzer 3: {self.model_analyzer_3}")
        print(f"⚖️ Decider: {self.model_decider}")
        
        self.listener = keyboard.Listener(on_press=self.on_key_press)
        self.listener.start()
        
        self.check_hotkey()
        
        try:
            self.root.mainloop()
        finally:
            if self.listener: self.listener.stop()
            if self.file_observer:
                self.file_observer.stop()
                self.file_observer.join()
    
    def launch_sharex_capture(self):
        """Launch ShareX region capture using CLI hotkey actions."""
        try:
            subprocess.Popen(['ShareX', '-RectangleRegion'])
            print("✓ ShareX rectangle region capture launched")
        except Exception as e:
            print(f"Error launching ShareX: {e}")

    def encode_image_to_base64(self, image_path):
        """Encode image file to base64 string."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding image: {e}")
            return None
    
    def get_image_mime_type(self, image_path):
        """Get MIME type based on file extension."""
        return {'.png': 'image/png', '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg'}.get(Path(image_path).suffix.lower(), 'image/png')
    
    def create_structured_analysis_prompt(self):
        """Prompt for the initial analyzer models, forcing a structured, step-by-step reasoning process."""
        return """You are a methodical Azure Solutions Architect. Your task is to perform a deep analysis of the provided exam question image. Do not rush to a conclusion. Follow the structured process below precisely.

**Step 1: Deconstruct the Scenario.**
Analyze the image and extract the key information. Fill out the following sections:
- **GOAL:** What is the primary objective to be achieved?
- **SITUATION:** What is the current environment or setup?
- **CONSTRAINTS:** What are the critical requirements or limitations (e.g., cost, performance, availability, security, specific service types like PaaS/IaaS)?

**Step 2: Evaluate Each Option.**
For each option presented in the question, provide a concise analysis of its pros and cons in the context of the deconstructed scenario.
- **Option A Analysis:**
- **Option B Analysis:**
- **Option C Analysis:**
- **Option D Analysis:** (and so on for all options)

**Step 3: Synthesize and Conclude.**
Based on your evaluation, which option is the *best* fit and why? Briefly explain your final reasoning.

**Step 4: State the Final Answer.**
On a new line, write the final answer prefixed with `RECOMMENDED_ANSWER:`. This is a mandatory last step.
"""

    # MODIFIED: New prompt with explicit multi-line examples and instructions.
    def create_final_arbiter_prompt(self, analysis1: str, analysis2: str, analysis3: str):
        """A highly-constrained prompt for the decider model to force a concise, multi-line answer."""
        return f"""You are an expert-level silent arbiter. Your only function is to identify the correct answer in the provided image and output it. The image is the absolute source of truth.

**Your output format is absolutely critical.**
- If the answer consists of multiple parts or commands, place each part on a **new line**.
- DO NOT provide explanation or commentary.
- DO NOT use prefixes like "ANSWER:".
- DO NOT use commas to separate answers.

**Example 1:** If the answer involves selecting "PolicyDefinitions" and "DC1", your entire response must be:
PolicyDefinitions
DC1

**Example 2:** If the answer is two PowerShell commands, your entire response must be:
Get-ADComputer server2.fabrikam.com
Get-ADComputer server1.contoso.com

**Example 3:** If the answer is a single choice like "Option C", your entire response must be:
C

Review the image and the provided text analyses to determine the final, correct answer.

---
**ANALYSIS 1:**
{analysis1}
---
**ANALYSIS 2:**
{analysis2}
---
**ANALYSIS 3:**
{analysis3}
---

Now, provide only the definitive answer from the image, following the strict formatting rules above.
"""
    
    def _extract_recommended_answer(self, analysis_text: str):
        """Finds the line starting with 'RECOMMENDED_ANSWER:' and returns the stripped answer."""
        try:
            for line in reversed(analysis_text.splitlines()):
                if line.upper().startswith("RECOMMENDED_ANSWER:"):
                    return line[len("RECOMMENDED_ANSWER:"):].strip()
        except:
            return None
        return None

    def analyze_with_openrouter(self, image_path):
        """Orchestrates the new 'deep think' analysis pipeline."""
        try:
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image: return "Error: Could not encode image"
            data_url = f"data:{self.get_image_mime_type(image_path)};base64,{base64_image}"
            
            analyses = {}

            def run_structured_analysis(model_name):
                """Target function for threading to get the new structured analysis."""
                try:
                    print(f"🤔 Forcing deep thought with {model_name}...")
                    completion = self.client.chat.completions.create(
                        model=model_name, messages=[{"role": "user", "content": [
                            {"type": "text", "text": self.create_structured_analysis_prompt()},
                            {"type": "image_url", "image_url": {"url": data_url, "detail": "high"}}
                        ]}], max_tokens=4096, temperature=0.05,
                    )
                    analyses[model_name] = completion.choices[0].message.content.strip() if completion.choices and completion.choices[0].message else f"Error: No response from {model_name}."
                except Exception as e:
                    analyses[model_name] = f"Error: API call failed for {model_name}: {e}"

            # --- STEP 1: Run all three structured analyses in parallel ---
            thread1 = threading.Thread(target=run_structured_analysis, args=(self.model_analyzer_1,))
            thread2 = threading.Thread(target=run_structured_analysis, args=(self.model_analyzer_2,))
            thread3 = threading.Thread(target=run_structured_analysis, args=(self.model_analyzer_3,))
            thread1.start(); thread2.start(); thread3.start()
            thread1.join(); thread2.join(); thread3.join()
            
            analysis1 = analyses.get(self.model_analyzer_1, "Analysis 1 failed.")
            analysis2 = analyses.get(self.model_analyzer_2, "Analysis 2 failed.")
            analysis3 = analyses.get(self.model_analyzer_3, "Analysis 3 failed.")
            print(f"✅ Deep thought process complete from all models.")

            # --- STEP 2: Check for a consensus ---
            final_answer = None
            parsed_answers = [
                self._extract_recommended_answer(analysis1),
                self._extract_recommended_answer(analysis2),
                self._extract_recommended_answer(analysis3)
            ]
            valid_answers = [ans for ans in parsed_answers if ans]

            if valid_answers:
                answer_counts = Counter(valid_answers)
                most_common = answer_counts.most_common(1)[0]
                if most_common[1] >= 2:
                    final_answer = most_common[0]
                    print(f"🎯 Consensus found after deep thought: '{final_answer}'. Skipping final arbiter.")

            # --- STEP 3: If no consensus, call the final arbiter model ---
            if not final_answer:
                print(f"⚖️ No consensus. Submitting all analyses to the final arbiter ({self.model_decider})...")
                
                decision_completion = self.client.chat.completions.create(
                    model=self.model_decider,
                    messages=[{
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.create_final_arbiter_prompt(analysis1, analysis2, analysis3)},
                            {"type": "image_url", "image_url": {"url": data_url, "detail": "high"}}
                        ]
                    }],
                    max_tokens=500, temperature=0.0,
                )

                if decision_completion.choices and decision_completion.choices[0].message:
                    final_answer = decision_completion.choices[0].message.content.strip()
                else:
                    raise ValueError(f"Final arbiter model ({self.model_decider}) returned an empty response.")

            # --- Display the final answer ---
            self.show_answer_bubble(final_answer)
            print(f"🎯 FINAL ANSWER: {final_answer}")
            print("🔄 Ready for next question (press Ctrl+D)")
            return final_answer

        except Exception as e:
            error_msg = f"Analysis pipeline failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_answer_bubble(error_msg)
            return f"Error: {error_msg}"

    def process_new_image(self, image_path):
        """Process newly captured image."""
        with self.processing_lock:
            try:
                print(f"📷 New screenshot detected: {image_path}")
                time.sleep(0.5) 
                if not os.path.exists(image_path):
                    print("❌ Image file not found"); return
                self.analyze_with_openrouter(image_path)
                try:
                    os.remove(image_path)
                    print(f"🗑️ Cleaned up screenshot: {os.path.basename(image_path)}")
                except Exception as e:
                    print(f"⚠ Could not delete image: {e}")
            except Exception as e:
                print(f"❌ Error processing image: {e}")
                if os.path.exists(image_path):
                    try: os.remove(image_path)
                    except: pass


class ShareXFileHandler(FileSystemEventHandler):
    """File system event handler for ShareX screenshots."""
    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.last_processed = None
    def on_created(self, event):
        if event.is_directory: return
        file_path = event.src_path
        if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            if file_path != self.last_processed:
                self.last_processed = file_path
                threading.Thread(target=self.analyzer.process_new_image, args=(file_path,), daemon=True).start()


def main():
    """Main function."""
    try:
        print("Azure Exam Analyzer - ShareX + OpenRouter Integration")
        print("=" * 55)
        try:
            import watchdog, requests, openai
            print("✓ All dependencies found")
        except ImportError as e:
            print(f"✗ Missing dependency: {e.name}\nPlease run: pip install watchdog requests openai"); return
        try:
            subprocess.run(['ShareX', '--version'], capture_output=True, timeout=3, check=True)
            print("✓ ShareX found and accessible")
        except Exception:
            print("⚠ ShareX may not be in PATH or is not installed.")
        print()
        analyzer = AzureExamAnalyzer()
        analyzer.start_listener()
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()